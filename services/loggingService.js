"use strict";

const db = require("../database/schemas");
const { user_activity_log, inventory_stage_log, item_modification_log, storage_operation_log } = db;

/**
 * Comprehensive Logging Service for Mover Inventory & Storage APP
 * 
 * This service provides logging functionality for all user activities
 * as required for troubleshooting and internal investigation.
 * 
 * Logs are accessible to user administrators (Master credentials) within the CMS.
 */
class LoggingService {
  
  /**
   * Log user login events
   * @param {Object} params - Login parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userType - Type of user (staff, admin, company)
   * @param {string} params.ipAddress - IP address
   * @param {string} params.userAgent - User agent string
   * @param {string} params.sessionId - Session ID
   */
  static async logUserLogin(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: params.userType || 'staff',
        action_type: 'login',
        ip_address: params.ipAddress,
        user_agent: params.userAgent,
        session_id: params.sessionId,
        action_details: {
          login_time: new Date(),
          device_info: params.deviceInfo || null
        }
      });
    } catch (error) {
      console.error('Error logging user login:', error);
      throw error;
    }
  }

  /**
   * Log user logout events
   * @param {Object} params - Logout parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userType - Type of user
   * @param {string} params.sessionId - Session ID
   */
  static async logUserLogout(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: params.userType || 'staff',
        action_type: 'logout',
        session_id: params.sessionId,
        action_details: {
          logout_time: new Date(),
          session_duration: params.sessionDuration || null
        }
      });
    } catch (error) {
      console.error('Error logging user logout:', error);
      throw error;
    }
  }

  /**
   * Log stage access events
   * @param {Object} params - Stage access parameters
   * @param {number} params.userId - User ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {string} params.stageType - Type of stage
   * @param {string} params.optionSelected - Option selected within stage
   */
  static async logStageAccess(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'stage_access',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        stage_name: params.stageName,
        action_details: {
          stage_type: params.stageType,
          option_selected: params.optionSelected,
          access_time: new Date()
        }
      });

      // Log in inventory stage logs for detailed tracking
      const stageLog = await inventory_stage_log.create({
        user_id: params.userId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        stage_type: params.stageType,
        access_time: new Date(),
        option_selected: params.optionSelected
      });

      return { userActivityLog, stageLog };
    } catch (error) {
      console.error('Error logging stage access:', error);
      throw error;
    }
  }

  /**
   * Log stage exit events
   * @param {Object} params - Stage exit parameters
   * @param {number} params.stageLogId - Stage log ID to update
   * @param {number} params.itemsProcessed - Number of items processed
   */
  static async logStageExit(params) {
    try {
      const exitTime = new Date();
      const stageLog = await inventory_stage_log.findByPk(params.stageLogId);
      
      if (stageLog) {
        const sessionDuration = Math.floor((exitTime - stageLog.access_time) / 1000); // in seconds
        
        return await stageLog.update({
          exit_time: exitTime,
          items_processed: params.itemsProcessed || 0,
          session_duration: sessionDuration
        });
      }
    } catch (error) {
      console.error('Error logging stage exit:', error);
      throw error;
    }
  }

  /**
   * Log item addition to inventory
   * @param {Object} params - Item addition parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {Object} params.itemData - Item data
   */
  static async logItemAddition(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'item_add',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        items_count: 1,
        action_details: {
          item_name: params.itemData.item_name,
          description: params.itemData.description,
          added_time: new Date()
        }
      });

      // Log in item modification logs
      const modificationLog = await item_modification_log.create({
        user_id: params.userId,
        item_id: params.itemId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        modification_type: 'create',
        new_values: params.itemData
      });

      return { userActivityLog, modificationLog };
    } catch (error) {
      console.error('Error logging item addition:', error);
      throw error;
    }
  }

  /**
   * Log item modification
   * @param {Object} params - Item modification parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {Object} params.oldValues - Old values
   * @param {Object} params.newValues - New values
   * @param {Array} params.fieldsModified - Fields that were modified
   */
  static async logItemModification(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'item_edit',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        action_details: {
          fields_modified: params.fieldsModified,
          modification_time: new Date()
        }
      });

      // Log in item modification logs
      const modificationLog = await item_modification_log.create({
        user_id: params.userId,
        item_id: params.itemId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        modification_type: 'update',
        fields_modified: params.fieldsModified,
        old_values: params.oldValues,
        new_values: params.newValues
      });

      return { userActivityLog, modificationLog };
    } catch (error) {
      console.error('Error logging item modification:', error);
      throw error;
    }
  }

  /**
   * Log item duplication
   * @param {Object} params - Item duplication parameters
   * @param {number} params.userId - User ID
   * @param {number} params.sourceItemId - Source item ID
   * @param {number} params.newItemId - New item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   */
  static async logItemDuplication(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'item_duplicate',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.newItemId,
        action_details: {
          source_item_id: params.sourceItemId,
          duplication_time: new Date()
        }
      });

      // Log in item modification logs
      const modificationLog = await item_modification_log.create({
        user_id: params.userId,
        item_id: params.newItemId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        modification_type: 'duplicate',
        source_item_id: params.sourceItemId
      });

      return { userActivityLog, modificationLog };
    } catch (error) {
      console.error('Error logging item duplication:', error);
      throw error;
    }
  }

  /**
   * Log item deletion
   * @param {Object} params - Item deletion parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {Object} params.itemData - Item data before deletion
   */
  static async logItemDeletion(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'item_delete',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        action_details: {
          item_name: params.itemData.item_name,
          deletion_time: new Date()
        }
      });

      // Log in item modification logs
      const modificationLog = await item_modification_log.create({
        user_id: params.userId,
        item_id: params.itemId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        modification_type: 'delete',
        old_values: params.itemData
      });

      return { userActivityLog, modificationLog };
    } catch (error) {
      console.error('Error logging item deletion:', error);
      throw error;
    }
  }

  /**
   * Log storage assignment operations
   * @param {Object} params - Storage assignment parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.unitId - Storage unit ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   * @param {number} params.previousUnitId - Previous unit ID if reassigning
   */
  static async logStorageAssignment(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'storage_assign',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        unit_id: params.unitId,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        action_details: {
          assignment_time: new Date(),
          previous_unit_id: params.previousUnitId,
          assignment_method: params.selectionMethod
        }
      });

      // Log in storage operation logs
      const storageLog = await storage_operation_log.create({
        user_id: params.userId,
        item_id: params.itemId,
        unit_id: params.unitId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        operation_type: 'assign_to_storage',
        selection_method: params.selectionMethod,
        qr_code_id: params.qrCodeId,
        previous_unit_id: params.previousUnitId
      });

      return { userActivityLog, storageLog };
    } catch (error) {
      console.error('Error logging storage assignment:', error);
      throw error;
    }
  }

  /**
   * Log storage removal operations
   * @param {Object} params - Storage removal parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.unitId - Storage unit ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   */
  static async logStorageRemoval(params) {
    try {
      // Log in user activity logs
      const userActivityLog = await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'storage_unassign',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        unit_id: params.unitId,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        action_details: {
          removal_time: new Date(),
          removal_method: params.selectionMethod
        }
      });

      // Log in storage operation logs
      const storageLog = await storage_operation_log.create({
        user_id: params.userId,
        item_id: params.itemId,
        unit_id: params.unitId,
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        operation_type: 'remove_from_storage',
        selection_method: params.selectionMethod,
        qr_code_id: params.qrCodeId
      });

      return { userActivityLog, storageLog };
    } catch (error) {
      console.error('Error logging storage removal:', error);
      throw error;
    }
  }

  /**
   * Log inventory removal operations
   * @param {Object} params - Inventory removal parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   */
  static async logInventoryRemoval(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'inventory_remove',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        action_details: {
          removal_time: new Date(),
          removal_method: params.selectionMethod
        }
      });
    } catch (error) {
      console.error('Error logging inventory removal:', error);
      throw error;
    }
  }

  /**
   * Log view items operations
   * @param {Object} params - View items parameters
   * @param {number} params.userId - User ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {number} params.itemsViewed - Number of items viewed
   */
  static async logViewItems(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'view_items',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        stage_name: params.stageName,
        items_count: params.itemsViewed || 0,
        action_details: {
          view_time: new Date(),
          items_viewed_count: params.itemsViewed
        }
      });
    } catch (error) {
      console.error('Error logging view items:', error);
      throw error;
    }
  }

  /**
   * Log QR code scanning operations
   * @param {Object} params - QR scan parameters
   * @param {number} params.userId - User ID
   * @param {number} params.qrCodeId - QR code ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.scanType - Type of scan (item/unit)
   * @param {number} params.targetId - Target item or unit ID
   */
  static async logQRCodeScan(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'scan_qr_code',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        qr_code_id: params.qrCodeId,
        selection_method: 'qr_scan',
        action_details: {
          scan_time: new Date(),
          scan_type: params.scanType,
          target_id: params.targetId
        }
      });
    } catch (error) {
      console.error('Error logging QR code scan:', error);
      throw error;
    }
  }

  /**
   * Log manual selection operations
   * @param {Object} params - Manual selection parameters
   * @param {number} params.userId - User ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionType - Type of selection (item/unit)
   * @param {number} params.targetId - Target item or unit ID
   */
  static async logManualSelection(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'manual_selection',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        selection_method: 'manual',
        action_details: {
          selection_time: new Date(),
          selection_type: params.selectionType,
          target_id: params.targetId
        }
      });
    } catch (error) {
      console.error('Error logging manual selection:', error);
      throw error;
    }
  }

  /**
   * Get user activity logs with filtering and pagination
   * @param {Object} filters - Filter parameters
   * @param {number} filters.userId - User ID filter
   * @param {string} filters.actionType - Action type filter
   * @param {number} filters.shipmentJobId - Shipment job ID filter
   * @param {Date} filters.startDate - Start date filter
   * @param {Date} filters.endDate - End date filter
   * @param {number} filters.page - Page number
   * @param {number} filters.limit - Items per page
   */
  static async getUserActivityLogs(filters = {}) {
    try {
      const whereClause = {};

      if (filters.userId) whereClause.user_id = filters.userId;
      if (filters.actionType) whereClause.action_type = filters.actionType;
      if (filters.shipmentJobId) whereClause.shipment_job_id = filters.shipmentJobId;

      if (filters.startDate || filters.endDate) {
        whereClause.created_at = {};
        if (filters.startDate) whereClause.created_at[db.Sequelize.Op.gte] = filters.startDate;
        if (filters.endDate) whereClause.created_at[db.Sequelize.Op.lte] = filters.endDate;
      }

      const page = filters.page || 1;
      const limit = filters.limit || 50;
      const offset = (page - 1) * limit;

      return await user_activity_log.findAndCountAll({
        where: whereClause,
        include: [
          { model: db.staff, as: 'staff_user', attributes: ['staff_id', 'first_name', 'last_name', 'email'] },
          { model: db.shipment_job, as: 'shipment_job', attributes: ['shipment_job_id', 'job_number'] },
          { model: db.shipment_type_stage_for_shipment, as: 'stage', attributes: ['local_shipment_stage_id', 'name'] }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });
    } catch (error) {
      console.error('Error getting user activity logs:', error);
      throw error;
    }
  }
}

module.exports = LoggingService;
