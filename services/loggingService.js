"use strict";

const db = require("../database/schemas");
const { comprehensive_activity_log } = db;

/**
 * Comprehensive Logging Service for Mover Inventory & Storage APP
 *
 * This service provides logging functionality for all user activities
 * as required for troubleshooting and internal investigation.
 *
 * Logs are accessible to user administrators (Master credentials) within the CMS.
 *
 * All logging requirements are handled through a single comprehensive table:
 * - User login/logout events with date and time
 * - Stage access and modifications tracking
 * - Item operations (add, edit, duplicate, delete) with field-level tracking
 * - Storage operations (assign/unassign) with selection method tracking
 * - QR code scanning vs manual selection tracking
 * - View operations tracking
 */
class LoggingService {
  
  /**
   * Log user login events
   * Requirement: Log user login events with date and time
   * @param {Object} params - Login parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userType - Type of user (staff, admin, company)
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {string} params.ipAddress - IP address
   * @param {string} params.userAgent - User agent string
   * @param {string} params.sessionId - Session ID
   */
  static async logUserLogin(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: params.userType || 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'authentication',
        action_type: 'login',
        ip_address: params.ipAddress,
        user_agent: params.userAgent,
        session_id: params.sessionId,
        additional_details: {
          login_time: new Date(),
          device_info: params.deviceInfo || null
        }
      });
    } catch (error) {
      console.error('Error logging user login:', error);
      throw error;
    }
  }

  /**
   * Log user logout events
   * @param {Object} params - Logout parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userType - Type of user
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {string} params.sessionId - Session ID
   * @param {number} params.sessionDuration - Session duration in seconds
   */
  static async logUserLogout(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: params.userType || 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'authentication',
        action_type: 'logout',
        session_id: params.sessionId,
        session_duration_seconds: params.sessionDuration,
        additional_details: {
          logout_time: new Date(),
          session_duration_seconds: params.sessionDuration || null
        }
      });
    } catch (error) {
      console.error('Error logging user logout:', error);
      throw error;
    }
  }

  /**
   * Log stage access events
   * Requirement: Track actions performed at the shipment level, including stage access and modifications
   * @param {Object} params - Stage access parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {string} params.stageType - Type of stage (add_items_to_inventory, add_items_to_storage, etc.)
   * @param {string} params.optionSelected - Option selected within stage
   */
  static async logStageAccess(params) {
    try {
      // Determine specific action type based on stage type
      let actionType = 'stage_access';
      if (params.stageType === 'add_items_to_inventory') {
        actionType = 'add_items_to_inventory_access';
      } else if (params.stageType === 'add_items_to_storage') {
        actionType = 'add_items_to_storage_access';
      } else if (params.stageType === 'remove_items_from_storage') {
        actionType = 'remove_items_from_storage_access';
      } else if (params.stageType === 'remove_items_from_inventory') {
        actionType = 'remove_items_from_inventory_access';
      }

      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'stage_navigation',
        action_type: actionType,
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        stage_type: params.stageType,
        stage_access_time: new Date(),
        option_selected: params.optionSelected,
        additional_details: {
          access_time: new Date(),
          stage_accessed: params.stageName
        }
      });
    } catch (error) {
      console.error('Error logging stage access:', error);
      throw error;
    }
  }

  /**
   * Log stage exit events
   * @param {Object} params - Stage exit parameters
   * @param {number} params.logId - Activity log ID to update
   * @param {number} params.itemsProcessed - Number of items processed
   */
  static async logStageExit(params) {
    try {
      const exitTime = new Date();
      const activityLog = await comprehensive_activity_log.findByPk(params.logId);

      if (activityLog && activityLog.stage_access_time) {
        const sessionDuration = Math.floor((exitTime - activityLog.stage_access_time) / 1000); // in seconds

        return await activityLog.update({
          stage_exit_time: exitTime,
          items_count: params.itemsProcessed || 0,
          session_duration_seconds: sessionDuration,
          action_type: 'stage_exit',
          additional_details: {
            ...activityLog.additional_details,
            exit_time: exitTime,
            items_processed: params.itemsProcessed || 0,
            session_duration_seconds: sessionDuration
          }
        });
      }
    } catch (error) {
      console.error('Error logging stage exit:', error);
      throw error;
    }
  }

  /**
   * Log item addition to inventory
   * Requirement: Record the number of items added to inventory by each user
   * @param {Object} params - Item addition parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.itemId - Item ID
   * @param {string} params.itemName - Item name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {Object} params.itemData - Item data
   */
  static async logItemAddition(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'item_operations',
        action_type: 'item_add',
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.itemId,
        item_name: params.itemName,
        items_count: 1,
        new_values: params.itemData,
        additional_details: {
          item_name: params.itemData.item_name,
          description: params.itemData.description,
          added_time: new Date(),
          stage_type: 'add_items_to_inventory'
        }
      });
    } catch (error) {
      console.error('Error logging item addition:', error);
      throw error;
    }
  }

  /**
   * Log item modification
   * Requirement: Capture any edits made to items (including fields modified such as description, notes, volume/weight)
   * @param {Object} params - Item modification parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.itemId - Item ID
   * @param {string} params.itemName - Item name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {Object} params.oldValues - Old values
   * @param {Object} params.newValues - New values
   * @param {Array} params.fieldsModified - Fields that were modified
   */
  static async logItemModification(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'item_operations',
        action_type: 'item_edit',
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.itemId,
        item_name: params.itemName,
        fields_modified: params.fieldsModified,
        old_values: params.oldValues,
        new_values: params.newValues,
        additional_details: {
          fields_modified: params.fieldsModified,
          modification_time: new Date(),
          changes_summary: `Modified fields: ${params.fieldsModified.join(', ')}`
        }
      });
    } catch (error) {
      console.error('Error logging item modification:', error);
      throw error;
    }
  }

  /**
   * Log item duplication
   * Requirement: Track item duplication events
   * @param {Object} params - Item duplication parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.sourceItemId - Source item ID
   * @param {string} params.sourceItemName - Source item name
   * @param {number} params.newItemId - New item ID
   * @param {string} params.newItemName - New item name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   */
  static async logItemDuplication(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'item_operations',
        action_type: 'item_duplicate',
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.newItemId,
        item_name: params.newItemName,
        source_item_id: params.sourceItemId,
        additional_details: {
          source_item_id: params.sourceItemId,
          source_item_name: params.sourceItemName,
          duplication_time: new Date()
        }
      });
    } catch (error) {
      console.error('Error logging item duplication:', error);
      throw error;
    }
  }

  /**
   * Log item deletion
   * Requirement: Track item deletion events
   * @param {Object} params - Item deletion parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.itemId - Item ID
   * @param {string} params.itemName - Item name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {Object} params.itemData - Item data before deletion
   */
  static async logItemDeletion(params) {
    try {
      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'item_operations',
        action_type: 'item_delete',
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.itemId,
        item_name: params.itemName,
        old_values: params.itemData,
        additional_details: {
          item_name: params.itemData.item_name,
          deletion_time: new Date(),
          deleted_item_data: params.itemData
        }
      });
    } catch (error) {
      console.error('Error logging item deletion:', error);
      throw error;
    }
  }

  /**
   * Log storage assignment operations
   * Requirement: If assigned to units, record whether the user selected the unit manually or scanned the unit QR code
   * @param {Object} params - Storage assignment parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.itemId - Item ID
   * @param {string} params.itemName - Item name
   * @param {number} params.unitId - Storage unit ID
   * @param {string} params.unitName - Storage unit name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   * @param {number} params.previousUnitId - Previous unit ID if reassigning
   */
  static async logStorageAssignment(params) {
    try {
      // Determine action type based on selection method
      const actionType = params.selectionMethod === 'qr_scan' ? 'storage_assign_qr_scan' : 'storage_assign_manual';

      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'storage_operations',
        action_type: actionType,
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.itemId,
        item_name: params.itemName,
        unit_id: params.unitId,
        unit_name: params.unitName,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        previous_unit_id: params.previousUnitId,
        additional_details: {
          assignment_time: new Date(),
          previous_unit_id: params.previousUnitId,
          assignment_method: params.selectionMethod,
          stage_type: 'add_items_to_storage'
        }
      });
    } catch (error) {
      console.error('Error logging storage assignment:', error);
      throw error;
    }
  }

  /**
   * Log storage removal operations
   * Requirement: If unassigning items, track whether they selected the item manually or scanned the item QR code
   * @param {Object} params - Storage removal parameters
   * @param {number} params.userId - User ID
   * @param {string} params.userName - User's full name
   * @param {string} params.userEmail - User's email
   * @param {number} params.itemId - Item ID
   * @param {string} params.itemName - Item name
   * @param {number} params.unitId - Storage unit ID
   * @param {string} params.unitName - Storage unit name
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {string} params.shipmentJobNumber - Shipment job number
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   */
  static async logStorageRemoval(params) {
    try {
      // Determine action type based on selection method
      const actionType = params.selectionMethod === 'qr_scan' ? 'storage_unassign_qr_scan' : 'storage_unassign_manual';

      return await comprehensive_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        user_name: params.userName,
        user_email: params.userEmail,
        action_category: 'storage_operations',
        action_type: actionType,
        shipment_job_id: params.shipmentJobId,
        shipment_job_number: params.shipmentJobNumber,
        stage_id: params.stageId,
        stage_name: params.stageName,
        item_id: params.itemId,
        item_name: params.itemName,
        unit_id: params.unitId,
        unit_name: params.unitName,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        additional_details: {
          removal_time: new Date(),
          removal_method: params.selectionMethod,
          stage_type: 'remove_items_from_storage'
        }
      });
    } catch (error) {
      console.error('Error logging storage removal:', error);
      throw error;
    }
  }

  /**
   * Log inventory removal operations
   * @param {Object} params - Inventory removal parameters
   * @param {number} params.userId - User ID
   * @param {number} params.itemId - Item ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionMethod - Selection method (manual/qr_scan)
   * @param {number} params.qrCodeId - QR code ID if applicable
   */
  static async logInventoryRemoval(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'inventory_remove',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        item_id: params.itemId,
        qr_code_id: params.qrCodeId,
        selection_method: params.selectionMethod,
        action_details: {
          removal_time: new Date(),
          removal_method: params.selectionMethod
        }
      });
    } catch (error) {
      console.error('Error logging inventory removal:', error);
      throw error;
    }
  }

  /**
   * Log view items operations
   * @param {Object} params - View items parameters
   * @param {number} params.userId - User ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.stageName - Stage name
   * @param {number} params.itemsViewed - Number of items viewed
   */
  static async logViewItems(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'view_items',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        stage_name: params.stageName,
        items_count: params.itemsViewed || 0,
        action_details: {
          view_time: new Date(),
          items_viewed_count: params.itemsViewed
        }
      });
    } catch (error) {
      console.error('Error logging view items:', error);
      throw error;
    }
  }

  /**
   * Log QR code scanning operations
   * @param {Object} params - QR scan parameters
   * @param {number} params.userId - User ID
   * @param {number} params.qrCodeId - QR code ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.scanType - Type of scan (item/unit)
   * @param {number} params.targetId - Target item or unit ID
   */
  static async logQRCodeScan(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'scan_qr_code',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        qr_code_id: params.qrCodeId,
        selection_method: 'qr_scan',
        action_details: {
          scan_time: new Date(),
          scan_type: params.scanType,
          target_id: params.targetId
        }
      });
    } catch (error) {
      console.error('Error logging QR code scan:', error);
      throw error;
    }
  }

  /**
   * Log manual selection operations
   * @param {Object} params - Manual selection parameters
   * @param {number} params.userId - User ID
   * @param {number} params.shipmentJobId - Shipment job ID
   * @param {number} params.stageId - Stage ID
   * @param {string} params.selectionType - Type of selection (item/unit)
   * @param {number} params.targetId - Target item or unit ID
   */
  static async logManualSelection(params) {
    try {
      return await user_activity_log.create({
        user_id: params.userId,
        user_type: 'staff',
        action_type: 'manual_selection',
        shipment_job_id: params.shipmentJobId,
        stage_id: params.stageId,
        selection_method: 'manual',
        action_details: {
          selection_time: new Date(),
          selection_type: params.selectionType,
          target_id: params.targetId
        }
      });
    } catch (error) {
      console.error('Error logging manual selection:', error);
      throw error;
    }
  }

  /**
   * Get user activity logs with filtering and pagination
   * @param {Object} filters - Filter parameters
   * @param {number} filters.userId - User ID filter
   * @param {string} filters.actionType - Action type filter
   * @param {number} filters.shipmentJobId - Shipment job ID filter
   * @param {Date} filters.startDate - Start date filter
   * @param {Date} filters.endDate - End date filter
   * @param {number} filters.page - Page number
   * @param {number} filters.limit - Items per page
   */
  static async getUserActivityLogs(filters = {}) {
    try {
      const whereClause = {};

      if (filters.userId) whereClause.user_id = filters.userId;
      if (filters.actionType) whereClause.action_type = filters.actionType;
      if (filters.shipmentJobId) whereClause.shipment_job_id = filters.shipmentJobId;

      if (filters.startDate || filters.endDate) {
        whereClause.created_at = {};
        if (filters.startDate) whereClause.created_at[db.Sequelize.Op.gte] = filters.startDate;
        if (filters.endDate) whereClause.created_at[db.Sequelize.Op.lte] = filters.endDate;
      }

      const page = filters.page || 1;
      const limit = filters.limit || 50;
      const offset = (page - 1) * limit;

      return await user_activity_log.findAndCountAll({
        where: whereClause,
        include: [
          { model: db.staff, as: 'staff_user', attributes: ['staff_id', 'first_name', 'last_name', 'email'] },
          { model: db.shipment_job, as: 'shipment_job', attributes: ['shipment_job_id', 'job_number'] },
          { model: db.shipment_type_stage_for_shipment, as: 'stage', attributes: ['local_shipment_stage_id', 'name'] }
        ],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });
    } catch (error) {
      console.error('Error getting user activity logs:', error);
      throw error;
    }
  }
}

module.exports = LoggingService;
