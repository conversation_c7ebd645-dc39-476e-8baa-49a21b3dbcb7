"use strict";
module.exports = (sequelize, DataTypes) => {
  const storage_operation_log = sequelize.define(
    "storage_operation_log",
    {
      storage_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: "Primary key for storage operation log entries"
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the user who performed the storage operation"
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the inventory item involved in storage operation"
      },
      unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Storage unit ID involved in the operation"
      },
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "Related shipment job ID"
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "Stage ID where the storage operation occurred"
      },
      operation_type: {
        type: DataTypes.ENUM("assign_to_storage", "remove_from_storage"),
        allowNull: false,
        comment: "Type of storage operation performed"
      },
      selection_method: {
        type: DataTypes.ENUM("manual", "qr_scan"),
        allowNull: false,
        comment: "Method used to select the unit/item - manual selection or QR code scanning"
      },
      qr_code_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "QR code ID if QR scanning was used"
      },
      previous_unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Previous storage unit ID (for reassignment operations)"
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the storage operation was performed"
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was last updated"
      }
    },
    {
      createdAt: "created_at",
      updatedAt: "updated_at",
      tableName: "storage_operation_logs",
      indexes: [
        {
          fields: ["user_id", "created_at"],
          name: "idx_storage_op_user_time"
        },
        {
          fields: ["item_id", "created_at"],
          name: "idx_storage_op_item_time"
        },
        {
          fields: ["shipment_job_id", "created_at"],
          name: "idx_storage_op_shipment_time"
        },
        {
          fields: ["operation_type", "created_at"],
          name: "idx_storage_op_type_time"
        }
      ]
    }
  );

  storage_operation_log.associate = function (models) {
    // Association with staff table
    storage_operation_log.belongsTo(models.staff, {
      as: "user",
      foreignKey: "user_id"
    });

    // Association with inventory item
    storage_operation_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "item_id"
    });

    // Association with current storage unit
    storage_operation_log.belongsTo(models.unit_list, {
      as: "storage_unit",
      foreignKey: "unit_id"
    });

    // Association with previous storage unit
    storage_operation_log.belongsTo(models.unit_list, {
      as: "previous_storage_unit",
      foreignKey: "previous_unit_id"
    });

    // Association with shipment job
    storage_operation_log.belongsTo(models.shipment_job, {
      as: "shipment_job",
      foreignKey: "shipment_job_id"
    });

    // Association with stage
    storage_operation_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "stage",
      foreignKey: "stage_id"
    });

    // Association with QR code
    storage_operation_log.belongsTo(models.qr_code, {
      as: "qr_code",
      foreignKey: "qr_code_id"
    });
  };

  return storage_operation_log;
};
