"use strict";
module.exports = (sequelize, DataTypes) => {
  const inventory_stage_log = sequelize.define(
    "inventory_stage_log",
    {
      stage_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: "Primary key for inventory stage log entries"
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the user who accessed the stage"
      },
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "Related shipment job ID"
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "Stage ID that was accessed"
      },
      stage_type: {
        type: DataTypes.ENUM(
          "add_items_to_inventory", 
          "add_items_to_storage", 
          "remove_items_from_storage", 
          "remove_items_from_inventory"
        ),
        allowNull: false,
        comment: "Type of stage accessed - maps to the 4 main inventory stages"
      },
      access_time: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "When the user accessed this stage"
      },
      exit_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the user exited this stage"
      },
      option_selected: {
        type: DataTypes.ENUM("add_items", "view_items", "assign_to_storage", "remove_from_storage", "remove_from_inventory"),
        allowNull: true,
        comment: "Option selected by user within the stage (add items, view items, etc.)"
      },
      items_processed: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "Number of items processed during this stage session"
      },
      session_duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Duration of stage session in seconds"
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was created"
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was last updated"
      }
    },
    {
      createdAt: "created_at",
      updatedAt: "updated_at",
      tableName: "inventory_stage_logs",
      indexes: [
        {
          fields: ["user_id", "access_time"],
          name: "idx_stage_log_user_access"
        },
        {
          fields: ["shipment_job_id", "access_time"],
          name: "idx_stage_log_shipment_access"
        },
        {
          fields: ["stage_id", "access_time"],
          name: "idx_stage_log_stage_access"
        },
        {
          fields: ["stage_type", "access_time"],
          name: "idx_stage_log_type_access"
        }
      ]
    }
  );

  inventory_stage_log.associate = function (models) {
    // Association with staff table
    inventory_stage_log.belongsTo(models.staff, {
      as: "user",
      foreignKey: "user_id"
    });

    // Association with shipment job
    inventory_stage_log.belongsTo(models.shipment_job, {
      as: "shipment_job",
      foreignKey: "shipment_job_id"
    });

    // Association with stage
    inventory_stage_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "stage",
      foreignKey: "stage_id"
    });
  };

  return inventory_stage_log;
};
