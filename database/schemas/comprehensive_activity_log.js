"use strict";
module.exports = (sequelize, DataTypes) => {
  const comprehensive_activity_log = sequelize.define(
    "comprehensive_activity_log",
    {
      log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: "Primary key for comprehensive activity log entries"
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the user who performed the action (from staff/admin/company table)"
      },
      user_type: {
        type: DataTypes.ENUM("staff", "admin", "company"),
        allowNull: false,
        defaultValue: "staff",
        comment: "Type of user - staff for APP users, admin for CMS users, company for company users"
      },
      user_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Full name of the user for easier identification"
      },
      user_email: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Email of the user for identification"
      },
      action_category: {
        type: DataTypes.ENUM(
          "authentication", 
          "stage_navigation", 
          "inventory_management", 
          "storage_operations", 
          "item_operations",
          "view_operations"
        ),
        allowNull: false,
        comment: "High-level category of the action performed"
      },
      action_type: {
        type: DataTypes.ENUM(
          "login", 
          "logout", 
          "stage_access", 
          "stage_exit",
          "add_items_to_inventory_access",
          "add_items_to_storage_access", 
          "remove_items_from_storage_access",
          "remove_items_from_inventory_access",
          "item_add", 
          "item_edit", 
          "item_duplicate", 
          "item_delete", 
          "storage_assign_manual", 
          "storage_assign_qr_scan",
          "storage_unassign_manual",
          "storage_unassign_qr_scan", 
          "inventory_remove_manual",
          "inventory_remove_qr_scan",
          "view_items_option_selected",
          "add_items_option_selected",
          "qr_code_scan",
          "manual_selection"
        ),
        allowNull: false,
        comment: "Specific type of action performed by the user"
      },
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Related shipment job ID if action is shipment-specific"
      },
      shipment_job_number: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Shipment job number for easier identification"
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Stage ID where the action was performed (from shipment_type_stage_for_shipment)"
      },
      stage_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the stage for easier identification"
      },
      stage_type: {
        type: DataTypes.ENUM(
          "add_items_to_inventory", 
          "add_items_to_storage", 
          "remove_items_from_storage", 
          "remove_items_from_inventory",
          "other"
        ),
        allowNull: true,
        comment: "Type of stage accessed - maps to the 4 main inventory stages"
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Inventory item ID if action is item-specific (from shipment_inventory)"
      },
      item_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the item for easier identification"
      },
      unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Storage unit ID if action involves storage units"
      },
      unit_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Storage unit name for easier identification"
      },
      qr_code_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "QR code ID if action involved QR code scanning"
      },
      selection_method: {
        type: DataTypes.ENUM("manual", "qr_scan"),
        allowNull: true,
        comment: "Method used for item/unit selection - manual selection or QR code scanning"
      },
      items_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "Number of items affected by the action (for bulk operations or items added to inventory)"
      },
      session_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Session ID to track user sessions"
      },
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: "IP address of the user when action was performed"
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "User agent string for device/browser identification"
      },
      stage_access_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the user accessed a stage (for stage access tracking)"
      },
      stage_exit_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "When the user exited a stage (for stage session tracking)"
      },
      session_duration_seconds: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Duration of stage session in seconds"
      },
      option_selected: {
        type: DataTypes.ENUM(
          "add_items", 
          "view_items", 
          "assign_to_storage", 
          "remove_from_storage", 
          "remove_from_inventory"
        ),
        allowNull: true,
        comment: "Option selected by user within the stage (add items, view items, etc.)"
      },
      fields_modified: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON array of field names that were modified during item edits"
      },
      old_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the old values before modification (for item edits)"
      },
      new_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the new values after modification (for item edits)"
      },
      source_item_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Source item ID for duplication operations"
      },
      previous_unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Previous storage unit ID (for reassignment operations)"
      },
      additional_details: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Additional details about the action in JSON format for extensibility"
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the action was performed"
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was last updated"
      }
    },
    {
      createdAt: "created_at",
      updatedAt: "updated_at",
      tableName: "comprehensive_activity_logs"
    }
  );

  comprehensive_activity_log.associate = function (models) {
    // Association with staff table for APP users
    comprehensive_activity_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "staff"
      }
    });

    // Association with admin table for CMS users
    comprehensive_activity_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "admin"
      }
    });

    // Association with company table for company users
    comprehensive_activity_log.belongsTo(models.company, {
      as: "company_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "company"
      }
    });

    // Association with shipment job
    comprehensive_activity_log.belongsTo(models.shipment_job, {
      as: "shipment_job",
      foreignKey: "shipment_job_id"
    });

    // Association with stage
    comprehensive_activity_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "stage",
      foreignKey: "stage_id"
    });

    // Association with inventory item
    comprehensive_activity_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "item_id"
    });

    // Association with source item for duplications
    comprehensive_activity_log.belongsTo(models.shipment_inventory, {
      as: "source_item",
      foreignKey: "source_item_id"
    });

    // Association with storage unit
    comprehensive_activity_log.belongsTo(models.unit_list, {
      as: "storage_unit",
      foreignKey: "unit_id"
    });

    // Association with previous storage unit
    comprehensive_activity_log.belongsTo(models.unit_list, {
      as: "previous_storage_unit",
      foreignKey: "previous_unit_id"
    });

    // Association with QR code
    comprehensive_activity_log.belongsTo(models.qr_code, {
      as: "qr_code",
      foreignKey: "qr_code_id"
    });
  };

  return comprehensive_activity_log;
};
