"use strict";
module.exports = (sequelize, DataTypes) => {
  const item_modification_log = sequelize.define(
    "item_modification_log",
    {
      modification_log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: "Primary key for item modification log entries"
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the user who modified the item"
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the inventory item that was modified"
      },
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "Related shipment job ID"
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Stage ID where the modification occurred"
      },
      modification_type: {
        type: DataTypes.ENUM("create", "update", "delete", "duplicate"),
        allowNull: false,
        comment: "Type of modification performed on the item"
      },
      fields_modified: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the fields that were modified with old and new values"
      },
      old_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the old values before modification"
      },
      new_values: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "JSON object containing the new values after modification"
      },
      source_item_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Source item ID for duplication operations"
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the modification was performed"
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was last updated"
      }
    },
    {
      createdAt: "created_at",
      updatedAt: "updated_at",
      tableName: "item_modification_logs",
      indexes: [
        {
          fields: ["user_id", "created_at"],
          name: "idx_item_mod_user_time"
        },
        {
          fields: ["item_id", "created_at"],
          name: "idx_item_mod_item_time"
        },
        {
          fields: ["shipment_job_id", "created_at"],
          name: "idx_item_mod_shipment_time"
        },
        {
          fields: ["modification_type", "created_at"],
          name: "idx_item_mod_type_time"
        }
      ]
    }
  );

  item_modification_log.associate = function (models) {
    // Association with staff table
    item_modification_log.belongsTo(models.staff, {
      as: "user",
      foreignKey: "user_id"
    });

    // Association with inventory item
    item_modification_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "item_id"
    });

    // Association with source item for duplications
    item_modification_log.belongsTo(models.shipment_inventory, {
      as: "source_item",
      foreignKey: "source_item_id"
    });

    // Association with shipment job
    item_modification_log.belongsTo(models.shipment_job, {
      as: "shipment_job",
      foreignKey: "shipment_job_id"
    });

    // Association with stage
    item_modification_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "stage",
      foreignKey: "stage_id"
    });
  };

  return item_modification_log;
};
