"use strict";
module.exports = (sequelize, DataTypes) => {
  const user_activity_log = sequelize.define(
    "user_activity_log",
    {
      log_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: "Primary key for user activity log entries"
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: "ID of the user who performed the action (from staff table)"
      },
      user_type: {
        type: DataTypes.ENUM("staff", "admin", "company"),
        allowNull: false,
        defaultValue: "staff",
        comment: "Type of user - staff for APP users, admin for CMS users, company for company users"
      },
      action_type: {
        type: DataTypes.ENUM(
          "login", 
          "logout", 
          "stage_access", 
          "item_add", 
          "item_edit", 
          "item_duplicate", 
          "item_delete", 
          "storage_assign", 
          "storage_unassign", 
          "inventory_remove", 
          "view_items",
          "scan_qr_code",
          "manual_selection"
        ),
        allowNull: false,
        comment: "Type of action performed by the user"
      },
      shipment_job_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Related shipment job ID if action is shipment-specific"
      },
      stage_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Stage ID where the action was performed (from shipment_type_stage_for_shipment)"
      },
      stage_name: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Name of the stage for easier identification"
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Inventory item ID if action is item-specific (from shipment_inventory)"
      },
      unit_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "Storage unit ID if action involves storage units"
      },
      qr_code_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "QR code ID if action involved QR code scanning"
      },
      selection_method: {
        type: DataTypes.ENUM("manual", "qr_scan"),
        allowNull: true,
        comment: "Method used for item/unit selection - manual selection or QR code scanning"
      },
      items_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: "Number of items affected by the action (for bulk operations)"
      },
      action_details: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "Additional details about the action in JSON format (e.g., fields modified, old/new values)"
      },
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: "IP address of the user when action was performed"
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "User agent string for device/browser identification"
      },
      session_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "Session ID to track user sessions"
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the action was performed"
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: "Timestamp when the log entry was last updated"
      }
    },
    {
      createdAt: "created_at",
      updatedAt: "updated_at",
      tableName: "user_activity_logs",
      indexes: [
        {
          fields: ["user_id", "created_at"],
          name: "idx_user_activity_user_time"
        },
        {
          fields: ["action_type", "created_at"],
          name: "idx_user_activity_action_time"
        },
        {
          fields: ["shipment_job_id", "created_at"],
          name: "idx_user_activity_shipment_time"
        },
        {
          fields: ["stage_id", "created_at"],
          name: "idx_user_activity_stage_time"
        }
      ]
    }
  );

  user_activity_log.associate = function (models) {
    // Association with staff table for APP users
    user_activity_log.belongsTo(models.staff, {
      as: "staff_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "staff"
      }
    });

    // Association with admin table for CMS users
    user_activity_log.belongsTo(models.admin, {
      as: "admin_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "admin"
      }
    });

    // Association with company table for company users
    user_activity_log.belongsTo(models.company, {
      as: "company_user",
      foreignKey: "user_id",
      constraints: false,
      scope: {
        user_type: "company"
      }
    });

    // Association with shipment job
    user_activity_log.belongsTo(models.shipment_job, {
      as: "shipment_job",
      foreignKey: "shipment_job_id"
    });

    // Association with stage
    user_activity_log.belongsTo(models.shipment_type_stage_for_shipment, {
      as: "stage",
      foreignKey: "stage_id"
    });

    // Association with inventory item
    user_activity_log.belongsTo(models.shipment_inventory, {
      as: "inventory_item",
      foreignKey: "item_id"
    });

    // Association with storage unit
    user_activity_log.belongsTo(models.unit_list, {
      as: "storage_unit",
      foreignKey: "unit_id"
    });

    // Association with QR code
    user_activity_log.belongsTo(models.qr_code, {
      as: "qr_code",
      foreignKey: "qr_code_id"
    });
  };

  return user_activity_log;
};
