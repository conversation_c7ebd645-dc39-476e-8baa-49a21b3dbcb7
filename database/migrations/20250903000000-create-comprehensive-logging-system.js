"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create user_activity_logs table for general user activities
      await queryInterface.createTable("user_activity_logs", {
        log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: "Primary key for user activity log entries"
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the user who performed the action (from staff table)"
        },
        user_type: {
          type: Sequelize.ENUM("staff", "admin", "company"),
          allowNull: false,
          defaultValue: "staff",
          comment: "Type of user - staff for APP users, admin for CMS users, company for company users"
        },
        action_type: {
          type: Sequelize.ENUM(
            "login", 
            "logout", 
            "stage_access", 
            "item_add", 
            "item_edit", 
            "item_duplicate", 
            "item_delete", 
            "storage_assign", 
            "storage_unassign", 
            "inventory_remove", 
            "view_items",
            "scan_qr_code",
            "manual_selection"
          ),
          allowNull: false,
          comment: "Type of action performed by the user"
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Related shipment job ID if action is shipment-specific"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage ID where the action was performed (from shipment_type_stage_for_shipment)"
        },
        stage_name: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Name of the stage for easier identification"
        },
        item_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Inventory item ID if action is item-specific (from shipment_inventory)"
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Storage unit ID if action involves storage units"
        },
        qr_code_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "QR code ID if action involved QR code scanning"
        },
        selection_method: {
          type: Sequelize.ENUM("manual", "qr_scan"),
          allowNull: true,
          comment: "Method used for item/unit selection - manual selection or QR code scanning"
        },
        items_count: {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
          comment: "Number of items affected by the action (for bulk operations)"
        },
        action_details: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Additional details about the action in JSON format (e.g., fields modified, old/new values)"
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: "IP address of the user when action was performed"
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "User agent string for device/browser identification"
        },
        session_id: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Session ID to track user sessions"
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the action was performed"
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was last updated"
        }
      });

      // Create inventory_stage_logs table for detailed stage-specific logging
      await queryInterface.createTable("inventory_stage_logs", {
        stage_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: "Primary key for inventory stage log entries"
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the user who accessed the stage"
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "Related shipment job ID"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "Stage ID that was accessed"
        },
        stage_type: {
          type: Sequelize.ENUM(
            "add_items_to_inventory", 
            "add_items_to_storage", 
            "remove_items_from_storage", 
            "remove_items_from_inventory"
          ),
          allowNull: false,
          comment: "Type of stage accessed - maps to the 4 main inventory stages"
        },
        access_time: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "When the user accessed this stage"
        },
        exit_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: "When the user exited this stage"
        },
        option_selected: {
          type: Sequelize.ENUM("add_items", "view_items", "assign_to_storage", "remove_from_storage", "remove_from_inventory"),
          allowNull: true,
          comment: "Option selected by user within the stage (add items, view items, etc.)"
        },
        items_processed: {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
          comment: "Number of items processed during this stage session"
        },
        session_duration: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Duration of stage session in seconds"
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was created"
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was last updated"
        }
      });

      // Create item_modification_logs table for detailed item change tracking
      await queryInterface.createTable("item_modification_logs", {
        modification_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: "Primary key for item modification log entries"
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the user who modified the item"
        },
        item_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the inventory item that was modified"
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "Related shipment job ID"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage ID where the modification occurred"
        },
        modification_type: {
          type: Sequelize.ENUM("create", "update", "delete", "duplicate"),
          allowNull: false,
          comment: "Type of modification performed on the item"
        },
        fields_modified: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the fields that were modified with old and new values"
        },
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the old values before modification"
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the new values after modification"
        },
        source_item_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Source item ID for duplication operations"
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the modification was performed"
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was last updated"
        }
      });

      // Create storage_operation_logs table for storage-related operations
      await queryInterface.createTable("storage_operation_logs", {
        storage_log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: "Primary key for storage operation log entries"
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the user who performed the storage operation"
        },
        item_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the inventory item involved in storage operation"
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Storage unit ID involved in the operation"
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "Related shipment job ID"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "Stage ID where the storage operation occurred"
        },
        operation_type: {
          type: Sequelize.ENUM("assign_to_storage", "remove_from_storage"),
          allowNull: false,
          comment: "Type of storage operation performed"
        },
        selection_method: {
          type: Sequelize.ENUM("manual", "qr_scan"),
          allowNull: false,
          comment: "Method used to select the unit/item - manual selection or QR code scanning"
        },
        qr_code_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "QR code ID if QR scanning was used"
        },
        previous_unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Previous storage unit ID (for reassignment operations)"
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the storage operation was performed"
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was last updated"
        }
      });

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("storage_operation_logs");
      await queryInterface.dropTable("item_modification_logs");
      await queryInterface.dropTable("inventory_stage_logs");
      await queryInterface.dropTable("user_activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
