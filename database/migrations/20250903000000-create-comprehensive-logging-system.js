"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Create comprehensive_activity_logs table for all logging requirements
      await queryInterface.createTable("comprehensive_activity_logs", {
        log_id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
          comment: "Primary key for comprehensive activity log entries"
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: "ID of the user who performed the action (from staff/admin/company table)"
        },
        user_type: {
          type: Sequelize.ENUM("staff", "admin", "company"),
          allowNull: false,
          defaultValue: "staff",
          comment: "Type of user - staff for APP users, admin for CMS users, company for company users"
        },
        user_name: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Full name of the user for easier identification"
        },
        user_email: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Email of the user for identification"
        },
        action_category: {
          type: Sequelize.ENUM(
            "authentication", 
            "stage_navigation", 
            "inventory_management", 
            "storage_operations", 
            "item_operations",
            "view_operations"
          ),
          allowNull: false,
          comment: "High-level category of the action performed"
        },
        action_type: {
          type: Sequelize.ENUM(
            "login", 
            "logout", 
            "stage_access", 
            "stage_exit",
            "add_items_to_inventory_access",
            "add_items_to_storage_access", 
            "remove_items_from_storage_access",
            "remove_items_from_inventory_access",
            "item_add", 
            "item_edit", 
            "item_duplicate", 
            "item_delete", 
            "storage_assign_manual", 
            "storage_assign_qr_scan",
            "storage_unassign_manual",
            "storage_unassign_qr_scan", 
            "inventory_remove_manual",
            "inventory_remove_qr_scan",
            "view_items_option_selected",
            "add_items_option_selected",
            "qr_code_scan",
            "manual_selection"
          ),
          allowNull: false,
          comment: "Specific type of action performed by the user"
        },
        shipment_job_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Related shipment job ID if action is shipment-specific"
        },
        shipment_job_number: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Shipment job number for easier identification"
        },
        stage_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Stage ID where the action was performed (from shipment_type_stage_for_shipment)"
        },
        stage_name: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Name of the stage for easier identification"
        },
        stage_type: {
          type: Sequelize.ENUM(
            "add_items_to_inventory", 
            "add_items_to_storage", 
            "remove_items_from_storage", 
            "remove_items_from_inventory",
            "other"
          ),
          allowNull: true,
          comment: "Type of stage accessed - maps to the 4 main inventory stages"
        },
        item_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Inventory item ID if action is item-specific (from shipment_inventory)"
        },
        item_name: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Name of the item for easier identification"
        },
        unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Storage unit ID if action involves storage units"
        },
        unit_name: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Storage unit name for easier identification"
        },
        qr_code_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "QR code ID if action involved QR code scanning"
        },
        selection_method: {
          type: Sequelize.ENUM("manual", "qr_scan"),
          allowNull: true,
          comment: "Method used for item/unit selection - manual selection or QR code scanning"
        },
        items_count: {
          type: Sequelize.INTEGER,
          allowNull: true,
          defaultValue: 0,
          comment: "Number of items affected by the action (for bulk operations or items added to inventory)"
        },
        session_id: {
          type: Sequelize.STRING,
          allowNull: true,
          comment: "Session ID to track user sessions"
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: "IP address of the user when action was performed"
        },
        user_agent: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: "User agent string for device/browser identification"
        },
        stage_access_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: "When the user accessed a stage (for stage access tracking)"
        },
        stage_exit_time: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: "When the user exited a stage (for stage session tracking)"
        },
        session_duration_seconds: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Duration of stage session in seconds"
        },
        option_selected: {
          type: Sequelize.ENUM(
            "add_items", 
            "view_items", 
            "assign_to_storage", 
            "remove_from_storage", 
            "remove_from_inventory"
          ),
          allowNull: true,
          comment: "Option selected by user within the stage (add items, view items, etc.)"
        },
        fields_modified: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON array of field names that were modified during item edits"
        },
        old_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the old values before modification (for item edits)"
        },
        new_values: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "JSON object containing the new values after modification (for item edits)"
        },
        source_item_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Source item ID for duplication operations"
        },
        previous_unit_id: {
          type: Sequelize.INTEGER,
          allowNull: true,
          comment: "Previous storage unit ID (for reassignment operations)"
        },
        additional_details: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "Additional details about the action in JSON format for extensibility"
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the action was performed"
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW,
          comment: "Timestamp when the log entry was last updated"
        }
      });

      // Create indexes for better query performance
      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["user_id", "created_at"],
        name: "idx_comprehensive_logs_user_time"
      });

      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["action_category", "action_type", "created_at"],
        name: "idx_comprehensive_logs_action_time"
      });

      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["shipment_job_id", "created_at"],
        name: "idx_comprehensive_logs_shipment_time"
      });

      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["stage_id", "stage_type", "created_at"],
        name: "idx_comprehensive_logs_stage_time"
      });

      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["item_id", "created_at"],
        name: "idx_comprehensive_logs_item_time"
      });

      await queryInterface.addIndex("comprehensive_activity_logs", {
        fields: ["user_type", "action_category", "created_at"],
        name: "idx_comprehensive_logs_user_type_action"
      });

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.dropTable("comprehensive_activity_logs");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
